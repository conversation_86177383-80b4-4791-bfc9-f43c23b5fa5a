<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务详情</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .status-bar {
            height: 44px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .floating-button {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 16px;
            border-radius: 16px;
            text-align: center;
            font-weight: bold;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .floating-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
        }
        .requirement-item {
            border-left: 4px solid #667eea;
            background: #f8faff;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 状态栏 -->
    <div class="status-bar">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-sm"></i>
            <i class="fas fa-wifi text-sm"></i>
            <i class="fas fa-battery-three-quarters text-sm"></i>
        </div>
    </div>

    <!-- 导航栏 -->
    <div class="gradient-bg px-6 py-4 text-white">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <i class="fas fa-arrow-left text-xl cursor-pointer"></i>
                <h1 class="text-lg font-semibold">任务详情</h1>
            </div>
            <div class="flex items-center space-x-4">
                <i class="fas fa-share-alt text-xl cursor-pointer"></i>
                <i class="fas fa-heart text-xl cursor-pointer"></i>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="px-6 py-6 pb-32">
        <!-- 任务基本信息 -->
        <div class="bg-white rounded-2xl p-6 shadow-sm mb-6">
            <div class="flex items-start justify-between mb-4">
                <div class="flex items-center space-x-3">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face" 
                         class="w-12 h-12 rounded-full object-cover">
                    <div>
                        <h2 class="text-xl font-bold text-gray-900">APP功能测试</h2>
                        <p class="text-sm text-gray-500">发布者：科技公司</p>
                        <div class="flex items-center mt-1">
                            <div class="flex text-yellow-400 text-sm">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="text-sm text-gray-500 ml-2">4.8分 (126评价)</span>
                        </div>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold text-green-600">¥50</div>
                    <div class="text-sm text-gray-500">单价</div>
                </div>
            </div>

            <!-- 任务标签 -->
            <div class="flex flex-wrap gap-2 mb-4">
                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">APP测试</span>
                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">远程任务</span>
                <span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm">新手友好</span>
            </div>

            <!-- 任务统计 -->
            <div class="grid grid-cols-4 gap-4 py-4 border-t border-gray-100">
                <div class="text-center">
                    <div class="text-lg font-bold text-gray-900">30分钟</div>
                    <div class="text-xs text-gray-500">预计时长</div>
                </div>
                <div class="text-center">
                    <div class="text-lg font-bold text-gray-900">15人</div>
                    <div class="text-xs text-gray-500">还需人数</div>
                </div>
                <div class="text-center">
                    <div class="text-lg font-bold text-gray-900">85人</div>
                    <div class="text-xs text-gray-500">已接取</div>
                </div>
                <div class="text-center">
                    <div class="text-lg font-bold text-gray-900">2天</div>
                    <div class="text-xs text-gray-500">剩余时间</div>
                </div>
            </div>
        </div>

        <!-- 任务描述 -->
        <div class="bg-white rounded-2xl p-6 shadow-sm mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-file-alt text-blue-600 mr-2"></i>任务描述
            </h3>
            <p class="text-gray-700 leading-relaxed mb-4">
                我们需要测试新版本APP的核心功能，确保用户体验流畅。测试内容包括但不限于：注册登录流程、主要功能操作、支付流程、界面响应速度等。
            </p>
            <p class="text-gray-700 leading-relaxed">
                测试完成后需要提供详细的测试报告，包括发现的问题、建议改进的地方以及整体使用感受。我们会根据报告质量给予相应的奖励。
            </p>
        </div>

        <!-- 任务要求 -->
        <div class="bg-white rounded-2xl p-6 shadow-sm mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-list-check text-blue-600 mr-2"></i>任务要求
            </h3>
            <div class="space-y-3">
                <div class="requirement-item pl-4 py-3 rounded-lg">
                    <div class="flex items-start space-x-3">
                        <i class="fas fa-mobile-alt text-blue-600 mt-1"></i>
                        <div>
                            <h4 class="font-medium text-gray-900">设备要求</h4>
                            <p class="text-sm text-gray-600">iOS 14.0+ 或 Android 8.0+ 系统</p>
                        </div>
                    </div>
                </div>
                <div class="requirement-item pl-4 py-3 rounded-lg">
                    <div class="flex items-start space-x-3">
                        <i class="fas fa-clock text-blue-600 mt-1"></i>
                        <div>
                            <h4 class="font-medium text-gray-900">时间要求</h4>
                            <p class="text-sm text-gray-600">需在24小时内完成测试并提交报告</p>
                        </div>
                    </div>
                </div>
                <div class="requirement-item pl-4 py-3 rounded-lg">
                    <div class="flex items-start space-x-3">
                        <i class="fas fa-file-text text-blue-600 mt-1"></i>
                        <div>
                            <h4 class="font-medium text-gray-900">报告要求</h4>
                            <p class="text-sm text-gray-600">详细记录测试过程，包含截图和问题描述</p>
                        </div>
                    </div>
                </div>
                <div class="requirement-item pl-4 py-3 rounded-lg">
                    <div class="flex items-start space-x-3">
                        <i class="fas fa-star text-blue-600 mt-1"></i>
                        <div>
                            <h4 class="font-medium text-gray-900">信用要求</h4>
                            <p class="text-sm text-gray-600">信用评分4.0分以上，完成率80%以上</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 发布者信息 -->
        <div class="bg-white rounded-2xl p-6 shadow-sm mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-user-tie text-blue-600 mr-2"></i>发布者信息
            </h3>
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face" 
                         class="w-12 h-12 rounded-full object-cover">
                    <div>
                        <h4 class="font-medium text-gray-900">科技公司</h4>
                        <p class="text-sm text-gray-500">认证企业用户</p>
                        <div class="flex items-center mt-1">
                            <span class="text-sm text-gray-600">信用评分：</span>
                            <span class="text-sm font-medium text-green-600 ml-1">4.9分</span>
                        </div>
                    </div>
                </div>
                <div class="text-right text-sm text-gray-500">
                    <div>发布任务：156个</div>
                    <div>完成率：98.5%</div>
                </div>
            </div>
        </div>

        <!-- 相似任务推荐 -->
        <div class="bg-white rounded-2xl p-6 shadow-sm">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-thumbs-up text-blue-600 mr-2"></i>相似任务推荐
            </h3>
            <div class="space-y-3">
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face" 
                             class="w-10 h-10 rounded-full object-cover">
                        <div>
                            <h4 class="font-medium text-gray-900 text-sm">网站功能测试</h4>
                            <p class="text-xs text-gray-500">电商平台</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-sm font-bold text-green-600">¥35</div>
                        <div class="text-xs text-gray-500">25分钟</div>
                    </div>
                </div>
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face" 
                             class="w-10 h-10 rounded-full object-cover">
                        <div>
                            <h4 class="font-medium text-gray-900 text-sm">小程序体验测试</h4>
                            <p class="text-xs text-gray-500">餐饮连锁</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-sm font-bold text-green-600">¥40</div>
                        <div class="text-xs text-gray-500">20分钟</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 浮动按钮 -->
    <div class="floating-button">
        <div class="flex items-center justify-center space-x-2">
            <i class="fas fa-hand-paper text-xl"></i>
            <span class="text-lg">立即接取任务</span>
        </div>
    </div>
</body>
</html>
