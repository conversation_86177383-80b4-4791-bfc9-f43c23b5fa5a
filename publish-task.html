<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发布任务</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .status-bar {
            height: 44px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .form-group {
            margin-bottom: 24px;
        }
        .form-label {
            display: block;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            font-size: 14px;
        }
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: white;
        }
        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }
        .category-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
        }
        .category-item {
            padding: 16px 12px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        .category-item:hover {
            border-color: #667eea;
            background: #f8faff;
        }
        .category-item.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .floating-button {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 16px;
            border-radius: 16px;
            text-align: center;
            font-weight: bold;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .floating-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 状态栏 -->
    <div class="status-bar">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-sm"></i>
            <i class="fas fa-wifi text-sm"></i>
            <i class="fas fa-battery-three-quarters text-sm"></i>
        </div>
    </div>

    <!-- 导航栏 -->
    <div class="gradient-bg px-6 py-4 text-white">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <i class="fas fa-arrow-left text-xl cursor-pointer"></i>
                <h1 class="text-lg font-semibold">发布任务</h1>
            </div>
            <div class="flex items-center space-x-4">
                <span class="text-sm bg-white/20 px-3 py-1 rounded-full">草稿</span>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="px-6 py-6 pb-32">
        <!-- 任务基本信息 -->
        <div class="bg-white rounded-2xl p-6 shadow-sm mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-6">
                <i class="fas fa-info-circle text-blue-600 mr-2"></i>基本信息
            </h2>
            
            <div class="form-group">
                <label class="form-label">任务标题 *</label>
                <input type="text" class="form-input" placeholder="请输入任务标题，简洁明了" value="APP功能测试">
            </div>

            <div class="form-group">
                <label class="form-label">任务分类 *</label>
                <div class="category-grid">
                    <div class="category-item selected">
                        <i class="fas fa-mobile-alt text-xl mb-2"></i>
                        <div class="text-sm">APP测试</div>
                    </div>
                    <div class="category-item">
                        <i class="fas fa-pen text-xl mb-2"></i>
                        <div class="text-sm">文案写作</div>
                    </div>
                    <div class="category-item">
                        <i class="fas fa-camera text-xl mb-2"></i>
                        <div class="text-sm">拍照任务</div>
                    </div>
                    <div class="category-item">
                        <i class="fas fa-chart-line text-xl mb-2"></i>
                        <div class="text-sm">数据录入</div>
                    </div>
                    <div class="category-item">
                        <i class="fas fa-search text-xl mb-2"></i>
                        <div class="text-sm">信息收集</div>
                    </div>
                    <div class="category-item">
                        <i class="fas fa-comments text-xl mb-2"></i>
                        <div class="text-sm">问卷调研</div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-2 gap-4">
                <div class="form-group">
                    <label class="form-label">单价报酬 *</label>
                    <div class="relative">
                        <span class="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500">¥</span>
                        <input type="number" class="form-input pl-8" placeholder="0" value="50">
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">需要人数 *</label>
                    <input type="number" class="form-input" placeholder="1" value="15">
                </div>
            </div>

            <div class="form-group">
                <label class="form-label">预计时长</label>
                <select class="form-input">
                    <option>30分钟以内</option>
                    <option>1小时以内</option>
                    <option>2小时以内</option>
                    <option>半天</option>
                    <option>1天</option>
                    <option>多天</option>
                </select>
            </div>
        </div>

        <!-- 任务详情 -->
        <div class="bg-white rounded-2xl p-6 shadow-sm mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-6">
                <i class="fas fa-file-alt text-blue-600 mr-2"></i>任务详情
            </h2>
            
            <div class="form-group">
                <label class="form-label">任务描述 *</label>
                <textarea class="form-input form-textarea" placeholder="详细描述任务内容、要求和注意事项...">我们需要测试新版本APP的核心功能，确保用户体验流畅。测试内容包括但不限于：注册登录流程、主要功能操作、支付流程、界面响应速度等。

测试完成后需要提供详细的测试报告，包括发现的问题、建议改进的地方以及整体使用感受。</textarea>
            </div>

            <div class="form-group">
                <label class="form-label">任务要求</label>
                <div class="space-y-3">
                    <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                        <i class="fas fa-mobile-alt text-blue-600"></i>
                        <input type="text" class="flex-1 bg-transparent border-none outline-none" placeholder="添加具体要求..." value="iOS 14.0+ 或 Android 8.0+ 系统">
                    </div>
                    <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                        <i class="fas fa-clock text-blue-600"></i>
                        <input type="text" class="flex-1 bg-transparent border-none outline-none" placeholder="添加具体要求..." value="需在24小时内完成测试并提交报告">
                    </div>
                    <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                        <i class="fas fa-plus text-blue-600 cursor-pointer"></i>
                        <input type="text" class="flex-1 bg-transparent border-none outline-none" placeholder="点击添加更多要求...">
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="form-label">附件上传</label>
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-2"></i>
                    <p class="text-gray-500 mb-2">点击上传或拖拽文件到此处</p>
                    <p class="text-sm text-gray-400">支持图片、文档等格式，单个文件不超过10MB</p>
                </div>
            </div>
        </div>

        <!-- 高级设置 -->
        <div class="bg-white rounded-2xl p-6 shadow-sm mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-6">
                <i class="fas fa-cog text-blue-600 mr-2"></i>高级设置
            </h2>
            
            <div class="form-group">
                <label class="form-label">截止时间</label>
                <input type="datetime-local" class="form-input" value="2024-01-15T18:00">
            </div>

            <div class="form-group">
                <label class="form-label">接取条件</label>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="text-sm text-gray-600">最低信用评分</label>
                        <select class="form-input mt-1">
                            <option>不限制</option>
                            <option>3.0分以上</option>
                            <option selected>4.0分以上</option>
                            <option>4.5分以上</option>
                        </select>
                    </div>
                    <div>
                        <label class="text-sm text-gray-600">最低完成率</label>
                        <select class="form-input mt-1">
                            <option>不限制</option>
                            <option>60%以上</option>
                            <option selected>80%以上</option>
                            <option>90%以上</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="form-label">任务标签</label>
                <div class="flex flex-wrap gap-2 mb-3">
                    <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm flex items-center">
                        APP测试
                        <i class="fas fa-times ml-2 cursor-pointer"></i>
                    </span>
                    <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm flex items-center">
                        远程任务
                        <i class="fas fa-times ml-2 cursor-pointer"></i>
                    </span>
                    <span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm flex items-center">
                        新手友好
                        <i class="fas fa-times ml-2 cursor-pointer"></i>
                    </span>
                </div>
                <input type="text" class="form-input" placeholder="输入标签后按回车添加">
            </div>
        </div>

        <!-- 费用预览 -->
        <div class="bg-white rounded-2xl p-6 shadow-sm">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-calculator text-blue-600 mr-2"></i>费用预览
            </h2>
            <div class="space-y-3">
                <div class="flex justify-between items-center">
                    <span class="text-gray-600">任务报酬 (¥50 × 15人)</span>
                    <span class="font-medium">¥750</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-gray-600">平台服务费 (5%)</span>
                    <span class="font-medium">¥37.5</span>
                </div>
                <div class="border-t pt-3 flex justify-between items-center">
                    <span class="text-lg font-semibold">总计费用</span>
                    <span class="text-xl font-bold text-green-600">¥787.5</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 浮动按钮 -->
    <div class="floating-button">
        <div class="flex items-center justify-center space-x-2">
            <i class="fas fa-paper-plane text-xl"></i>
            <span class="text-lg">发布任务</span>
        </div>
    </div>

    <script>
        // 分类选择
        document.querySelectorAll('.category-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.category-item').forEach(i => i.classList.remove('selected'));
                this.classList.add('selected');
            });
        });
    </script>
</body>
</html>
