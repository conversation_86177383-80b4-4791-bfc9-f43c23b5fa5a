<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的任务</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .status-bar {
            height: 44px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        .tab-bar {
            height: 80px;
            background: white;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-around;
            align-items: center;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
        }
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #9ca3af;
        }
        .tab-item.active {
            color: #667eea;
        }
        .tab-item i {
            font-size: 20px;
            margin-bottom: 4px;
        }
        .tab-item span {
            font-size: 12px;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .task-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            padding: 4px;
            margin: 0 24px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .task-tab {
            flex: 1;
            text-align: center;
            padding: 12px 8px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 14px;
        }
        .task-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .task-card {
            transition: all 0.3s ease;
        }
        .task-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-progress {
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
            color: white;
        }
        .status-completed {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }
        .status-pending {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
            color: white;
        }
        .status-rejected {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 状态栏 -->
    <div class="status-bar">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-sm"></i>
            <i class="fas fa-wifi text-sm"></i>
            <i class="fas fa-battery-three-quarters text-sm"></i>
        </div>
    </div>

    <!-- 头部区域 -->
    <div class="gradient-bg px-6 py-6 text-white">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h1 class="text-2xl font-bold">我的任务</h1>
                <p class="text-blue-100 text-sm">管理你的所有任务</p>
            </div>
            <div class="relative">
                <i class="fas fa-search text-xl cursor-pointer"></i>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="bg-white/20 backdrop-blur-sm rounded-2xl p-4">
            <div class="grid grid-cols-4 gap-4 text-center">
                <div>
                    <div class="text-xl font-bold">12</div>
                    <div class="text-xs text-blue-100">进行中</div>
                </div>
                <div>
                    <div class="text-xl font-bold">156</div>
                    <div class="text-xs text-blue-100">已完成</div>
                </div>
                <div>
                    <div class="text-xl font-bold">3</div>
                    <div class="text-xs text-blue-100">待审核</div>
                </div>
                <div>
                    <div class="text-xl font-bold">¥2,580</div>
                    <div class="text-xs text-blue-100">总收益</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 任务分类标签 -->
    <div class="-mt-6 mb-6">
        <div class="task-tabs">
            <div class="task-tab active">全部</div>
            <div class="task-tab">进行中</div>
            <div class="task-tab">已完成</div>
            <div class="task-tab">待审核</div>
        </div>
    </div>

    <!-- 任务列表 -->
    <div class="px-6 pb-32 space-y-4">
        <!-- 进行中的任务 -->
        <div class="task-card bg-white rounded-2xl p-5 shadow-sm border border-gray-100">
            <div class="flex items-start justify-between mb-3">
                <div class="flex items-center space-x-3">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" 
                         class="w-10 h-10 rounded-full object-cover">
                    <div>
                        <h3 class="font-semibold text-gray-900">APP功能测试</h3>
                        <p class="text-sm text-gray-500">科技公司</p>
                    </div>
                </div>
                <div class="text-right">
                    <div class="status-badge status-progress">进行中</div>
                    <div class="text-lg font-bold text-green-600 mt-1">¥50</div>
                </div>
            </div>
            <p class="text-gray-600 text-sm mb-3">需要测试新版本APP的核心功能，包括注册、登录、支付等流程...</p>
            
            <!-- 进度条 -->
            <div class="mb-3">
                <div class="flex justify-between text-sm text-gray-500 mb-1">
                    <span>任务进度</span>
                    <span>75%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full" style="width: 75%"></div>
                </div>
            </div>

            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4 text-sm text-gray-500">
                    <span><i class="fas fa-clock mr-1"></i>剩余8小时</span>
                    <span><i class="fas fa-calendar mr-1"></i>今天截止</span>
                </div>
                <button class="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-medium hover:bg-blue-700 transition-colors">
                    继续任务
                </button>
            </div>
        </div>

        <!-- 已完成的任务 -->
        <div class="task-card bg-white rounded-2xl p-5 shadow-sm border border-gray-100">
            <div class="flex items-start justify-between mb-3">
                <div class="flex items-center space-x-3">
                    <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face" 
                         class="w-10 h-10 rounded-full object-cover">
                    <div>
                        <h3 class="font-semibold text-gray-900">产品文案撰写</h3>
                        <p class="text-sm text-gray-500">电商平台</p>
                    </div>
                </div>
                <div class="text-right">
                    <div class="status-badge status-completed">已完成</div>
                    <div class="text-lg font-bold text-green-600 mt-1">¥80</div>
                </div>
            </div>
            <p class="text-gray-600 text-sm mb-3">为新上线的智能手表撰写产品介绍文案，要求突出产品特色...</p>
            
            <!-- 评价 -->
            <div class="bg-green-50 rounded-lg p-3 mb-3">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-green-800">任务评价</span>
                    <div class="flex text-yellow-400 text-sm">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>
                <p class="text-sm text-green-700">"文案质量很高，完全符合要求，表达清晰有力。"</p>
            </div>

            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4 text-sm text-gray-500">
                    <span><i class="fas fa-check mr-1"></i>昨天完成</span>
                    <span><i class="fas fa-star mr-1 text-yellow-400"></i>5.0评分</span>
                </div>
                <button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-full text-sm font-medium hover:bg-gray-200 transition-colors">
                    查看详情
                </button>
            </div>
        </div>

        <!-- 待审核的任务 -->
        <div class="task-card bg-white rounded-2xl p-5 shadow-sm border border-gray-100">
            <div class="flex items-start justify-between mb-3">
                <div class="flex items-center space-x-3">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face" 
                         class="w-10 h-10 rounded-full object-cover">
                    <div>
                        <h3 class="font-semibold text-gray-900">店铺拍照任务</h3>
                        <p class="text-sm text-gray-500">餐饮连锁</p>
                    </div>
                </div>
                <div class="text-right">
                    <div class="status-badge status-pending">待审核</div>
                    <div class="text-lg font-bold text-green-600 mt-1">¥25</div>
                </div>
            </div>
            <p class="text-gray-600 text-sm mb-3">需要到指定餐厅拍摄门店外观和内部环境照片...</p>
            
            <!-- 提交信息 -->
            <div class="bg-blue-50 rounded-lg p-3 mb-3">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-blue-800">提交状态</span>
                    <span class="text-sm text-blue-600">2小时前提交</span>
                </div>
                <p class="text-sm text-blue-700">已提交12张照片，等待发布者审核中...</p>
            </div>

            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4 text-sm text-gray-500">
                    <span><i class="fas fa-clock mr-1"></i>预计24小时内审核</span>
                </div>
                <button class="bg-blue-100 text-blue-700 px-4 py-2 rounded-full text-sm font-medium hover:bg-blue-200 transition-colors">
                    查看提交
                </button>
            </div>
        </div>

        <!-- 被拒绝的任务 -->
        <div class="task-card bg-white rounded-2xl p-5 shadow-sm border border-gray-100">
            <div class="flex items-start justify-between mb-3">
                <div class="flex items-center space-x-3">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" 
                         class="w-10 h-10 rounded-full object-cover">
                    <div>
                        <h3 class="font-semibold text-gray-900">数据录入任务</h3>
                        <p class="text-sm text-gray-500">数据公司</p>
                    </div>
                </div>
                <div class="text-right">
                    <div class="status-badge status-rejected">已拒绝</div>
                    <div class="text-lg font-bold text-gray-400 mt-1">¥30</div>
                </div>
            </div>
            <p class="text-gray-600 text-sm mb-3">录入客户信息到Excel表格中，要求准确无误...</p>
            
            <!-- 拒绝原因 -->
            <div class="bg-red-50 rounded-lg p-3 mb-3">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-red-800">拒绝原因</span>
                    <span class="text-sm text-red-600">3天前</span>
                </div>
                <p class="text-sm text-red-700">数据格式不符合要求，部分信息录入错误，请重新提交。</p>
            </div>

            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4 text-sm text-gray-500">
                    <span><i class="fas fa-exclamation-triangle mr-1"></i>可重新提交</span>
                </div>
                <button class="bg-red-600 text-white px-4 py-2 rounded-full text-sm font-medium hover:bg-red-700 transition-colors">
                    重新提交
                </button>
            </div>
        </div>

        <!-- 快速操作卡片 -->
        <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold mb-2">寻找更多任务？</h3>
                    <p class="text-blue-100 text-sm">浏览任务大厅，发现更多赚钱机会</p>
                </div>
                <button class="bg-white text-blue-600 px-4 py-2 rounded-full font-medium hover:bg-blue-50 transition-colors">
                    去看看
                </button>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="tab-bar">
        <div class="tab-item">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="tab-item active">
            <i class="fas fa-tasks"></i>
            <span>我的任务</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-plus-circle"></i>
            <span>发布</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-comments"></i>
            <span>消息</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>

    <script>
        // 任务分类标签切换
        document.querySelectorAll('.task-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.task-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>
