<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息中心</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .status-bar {
            height: 44px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        .tab-bar {
            height: 80px;
            background: white;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-around;
            align-items: center;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
        }
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #9ca3af;
        }
        .tab-item.active {
            color: #667eea;
        }
        .tab-item i {
            font-size: 20px;
            margin-bottom: 4px;
        }
        .tab-item span {
            font-size: 12px;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .message-item {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .message-item:hover {
            background: #f8faff;
        }
        .message-item.unread {
            background: #f0f9ff;
            border-left: 4px solid #0ea5e9;
        }
        .message-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            padding: 4px;
            margin: 0 24px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .message-tab {
            flex: 1;
            text-align: center;
            padding: 12px 8px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 14px;
        }
        .message-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .notification-dot {
            position: absolute;
            top: -2px;
            right: -2px;
            width: 8px;
            height: 8px;
            background: #ef4444;
            border-radius: 50%;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 状态栏 -->
    <div class="status-bar">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-sm"></i>
            <i class="fas fa-wifi text-sm"></i>
            <i class="fas fa-battery-three-quarters text-sm"></i>
        </div>
    </div>

    <!-- 头部区域 -->
    <div class="gradient-bg px-6 py-6 text-white">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h1 class="text-2xl font-bold">消息中心</h1>
                <p class="text-blue-100 text-sm">及时了解任务动态</p>
            </div>
            <div class="flex items-center space-x-4">
                <div class="relative">
                    <i class="fas fa-bell text-xl cursor-pointer"></i>
                    <span class="absolute -top-1 -right-1 bg-red-500 text-xs rounded-full w-4 h-4 flex items-center justify-center">5</span>
                </div>
                <i class="fas fa-cog text-xl cursor-pointer"></i>
            </div>
        </div>

        <!-- 快速统计 -->
        <div class="bg-white/20 backdrop-blur-sm rounded-2xl p-4">
            <div class="grid grid-cols-3 gap-4 text-center">
                <div>
                    <div class="text-xl font-bold">8</div>
                    <div class="text-xs text-blue-100">未读消息</div>
                </div>
                <div>
                    <div class="text-xl font-bold">3</div>
                    <div class="text-xs text-blue-100">系统通知</div>
                </div>
                <div>
                    <div class="text-xl font-bold">2</div>
                    <div class="text-xs text-blue-100">任务提醒</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息分类标签 -->
    <div class="-mt-6 mb-6">
        <div class="message-tabs">
            <div class="message-tab active">全部</div>
            <div class="message-tab">任务消息</div>
            <div class="message-tab">系统通知</div>
            <div class="message-tab">私信</div>
        </div>
    </div>

    <!-- 消息列表 -->
    <div class="px-6 pb-32">
        <!-- 任务相关消息 -->
        <div class="bg-white rounded-2xl shadow-sm mb-4 overflow-hidden">
            <div class="message-item unread p-4 border-b border-gray-100">
                <div class="flex items-start space-x-3">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" 
                             class="w-10 h-10 rounded-full object-cover">
                        <div class="notification-dot"></div>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-1">
                            <h3 class="font-semibold text-gray-900">任务审核通过</h3>
                            <span class="text-xs text-gray-500">5分钟前</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-2">您提交的"APP功能测试"任务已通过审核，报酬¥50已发放到账户。</p>
                        <div class="flex items-center space-x-4">
                            <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">任务完成</span>
                            <span class="text-xs text-blue-600 cursor-pointer">查看详情</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="message-item unread p-4 border-b border-gray-100">
                <div class="flex items-start space-x-3">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face" 
                             class="w-10 h-10 rounded-full object-cover">
                        <div class="notification-dot"></div>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-1">
                            <h3 class="font-semibold text-gray-900">新任务邀请</h3>
                            <span class="text-xs text-gray-500">1小时前</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-2">电商平台邀请您参与"产品评测"任务，报酬¥120，预计2小时完成。</p>
                        <div class="flex items-center space-x-4">
                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">任务邀请</span>
                            <span class="text-xs text-blue-600 cursor-pointer">立即查看</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="message-item p-4 border-b border-gray-100">
                <div class="flex items-start space-x-3">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face" 
                             class="w-10 h-10 rounded-full object-cover">
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-1">
                            <h3 class="font-semibold text-gray-900">任务提醒</h3>
                            <span class="text-xs text-gray-500">3小时前</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-2">您接取的"店铺拍照任务"将在6小时后截止，请及时完成提交。</p>
                        <div class="flex items-center space-x-4">
                            <span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">截止提醒</span>
                            <span class="text-xs text-blue-600 cursor-pointer">继续任务</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统通知 -->
        <div class="bg-white rounded-2xl shadow-sm mb-4 overflow-hidden">
            <div class="message-item unread p-4 border-b border-gray-100">
                <div class="flex items-start space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-gift text-white text-sm"></i>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-1">
                            <h3 class="font-semibold text-gray-900">新人奖励到账</h3>
                            <span class="text-xs text-gray-500">今天</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-2">恭喜您完成新手任务，获得新人奖励¥20，继续加油！</p>
                        <div class="flex items-center space-x-4">
                            <span class="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full">系统奖励</span>
                            <span class="text-xs text-blue-600 cursor-pointer">查看余额</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="message-item p-4 border-b border-gray-100">
                <div class="flex items-start space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-teal-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-star text-white text-sm"></i>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-1">
                            <h3 class="font-semibold text-gray-900">信用等级提升</h3>
                            <span class="text-xs text-gray-500">昨天</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-2">您的信用评分已提升至4.9分，解锁更多高价值任务！</p>
                        <div class="flex items-center space-x-4">
                            <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">等级提升</span>
                            <span class="text-xs text-blue-600 cursor-pointer">查看等级</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="message-item p-4">
                <div class="flex items-start space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-bullhorn text-white text-sm"></i>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-1">
                            <h3 class="font-semibold text-gray-900">平台公告</h3>
                            <span class="text-xs text-gray-500">2天前</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-2">平台将于本周末进行系统维护，维护期间部分功能可能受影响。</p>
                        <div class="flex items-center space-x-4">
                            <span class="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full">平台公告</span>
                            <span class="text-xs text-blue-600 cursor-pointer">查看详情</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 私信消息 -->
        <div class="bg-white rounded-2xl shadow-sm mb-4 overflow-hidden">
            <div class="message-item unread p-4 border-b border-gray-100">
                <div class="flex items-start space-x-3">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" 
                             class="w-10 h-10 rounded-full object-cover">
                        <div class="notification-dot"></div>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-1">
                            <h3 class="font-semibold text-gray-900">科技公司</h3>
                            <span class="text-xs text-gray-500">30分钟前</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-2">您好，关于APP测试任务，有几个细节想和您确认一下...</p>
                        <div class="flex items-center space-x-4">
                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">私信</span>
                            <span class="text-xs text-blue-600 cursor-pointer">回复消息</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="message-item p-4">
                <div class="flex items-start space-x-3">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face" 
                             class="w-10 h-10 rounded-full object-cover">
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-1">
                            <h3 class="font-semibold text-gray-900">电商平台</h3>
                            <span class="text-xs text-gray-500">2小时前</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-2">感谢您的优质文案，我们很满意，期待下次合作！</p>
                        <div class="flex items-center space-x-4">
                            <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">感谢信</span>
                            <span class="text-xs text-blue-600 cursor-pointer">查看对话</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold mb-2">消息设置</h3>
                    <p class="text-blue-100 text-sm">管理通知偏好和消息提醒</p>
                </div>
                <button class="bg-white text-blue-600 px-4 py-2 rounded-full font-medium hover:bg-blue-50 transition-colors">
                    设置
                </button>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="tab-bar">
        <div class="tab-item">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-tasks"></i>
            <span>我的任务</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-plus-circle"></i>
            <span>发布</span>
        </div>
        <div class="tab-item active">
            <i class="fas fa-comments"></i>
            <span>消息</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>

    <script>
        // 消息分类标签切换
        document.querySelectorAll('.message-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.message-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 消息点击事件
        document.querySelectorAll('.message-item').forEach(item => {
            item.addEventListener('click', function() {
                this.classList.remove('unread');
                // 这里可以添加跳转到详情页的逻辑
            });
        });
    </script>
</body>
</html>
