<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页 - 任务大厅</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .status-bar {
            height: 44px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        .tab-bar {
            height: 80px;
            background: white;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-around;
            align-items: center;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
        }
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #9ca3af;
        }
        .tab-item.active {
            color: #667eea;
        }
        .tab-item i {
            font-size: 20px;
            margin-bottom: 4px;
        }
        .tab-item span {
            font-size: 12px;
        }
        .content-area {
            padding-bottom: 80px;
        }
        .task-card {
            transition: all 0.3s ease;
        }
        .task-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .category-chip {
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 状态栏 -->
    <div class="status-bar">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-sm"></i>
            <i class="fas fa-wifi text-sm"></i>
            <i class="fas fa-battery-three-quarters text-sm"></i>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-area">
        <!-- 头部搜索区域 -->
        <div class="gradient-bg px-6 py-6 text-white">
            <div class="flex items-center justify-between mb-4">
                <div>
                    <h1 class="text-2xl font-bold">任务大厅</h1>
                    <p class="text-blue-100 text-sm">发现更多赚钱机会</p>
                </div>
                <div class="relative">
                    <i class="fas fa-bell text-xl"></i>
                    <span class="absolute -top-1 -right-1 bg-red-500 text-xs rounded-full w-4 h-4 flex items-center justify-center">3</span>
                </div>
            </div>
            
            <!-- 搜索框 -->
            <div class="relative">
                <input type="text" placeholder="搜索任务..." 
                       class="w-full bg-white/20 backdrop-blur-sm border border-white/30 rounded-full px-4 py-3 pl-12 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50">
                <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-white/70"></i>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="px-6 -mt-6 mb-6">
            <div class="bg-white rounded-2xl p-6 shadow-lg">
                <div class="grid grid-cols-3 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">¥2,580</div>
                        <div class="text-sm text-gray-500">今日收益</div>
                    </div>
                    <div class="text-center border-l border-r border-gray-200">
                        <div class="text-2xl font-bold text-blue-600">156</div>
                        <div class="text-sm text-gray-500">完成任务</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600">4.9</div>
                        <div class="text-sm text-gray-500">信用评分</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分类标签 -->
        <div class="px-6 mb-6">
            <div class="flex space-x-3 overflow-x-auto pb-2">
                <div class="category-chip text-white px-4 py-2 rounded-full text-sm whitespace-nowrap">
                    <i class="fas fa-fire mr-2"></i>热门
                </div>
                <div class="bg-gray-200 text-gray-700 px-4 py-2 rounded-full text-sm whitespace-nowrap">
                    <i class="fas fa-mobile-alt mr-2"></i>APP测试
                </div>
                <div class="bg-gray-200 text-gray-700 px-4 py-2 rounded-full text-sm whitespace-nowrap">
                    <i class="fas fa-pen mr-2"></i>文案写作
                </div>
                <div class="bg-gray-200 text-gray-700 px-4 py-2 rounded-full text-sm whitespace-nowrap">
                    <i class="fas fa-camera mr-2"></i>拍照任务
                </div>
            </div>
        </div>

        <!-- 任务列表 -->
        <div class="px-6 space-y-4">
            <!-- 任务卡片1 -->
            <div class="task-card bg-white rounded-2xl p-5 shadow-sm border border-gray-100">
                <div class="flex items-start justify-between mb-3">
                    <div class="flex items-center space-x-3">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" 
                             class="w-10 h-10 rounded-full object-cover">
                        <div>
                            <h3 class="font-semibold text-gray-900">APP功能测试</h3>
                            <p class="text-sm text-gray-500">科技公司</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-lg font-bold text-green-600">¥50</div>
                        <div class="text-xs text-gray-500">单价</div>
                    </div>
                </div>
                <p class="text-gray-600 text-sm mb-3">需要测试新版本APP的核心功能，包括注册、登录、支付等流程，提供详细的测试报告。</p>
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                        <span><i class="fas fa-clock mr-1"></i>30分钟</span>
                        <span><i class="fas fa-users mr-1"></i>还需15人</span>
                        <span><i class="fas fa-star mr-1 text-yellow-400"></i>4.8</span>
                    </div>
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-medium hover:bg-blue-700 transition-colors">
                        立即接取
                    </button>
                </div>
            </div>

            <!-- 任务卡片2 -->
            <div class="task-card bg-white rounded-2xl p-5 shadow-sm border border-gray-100">
                <div class="flex items-start justify-between mb-3">
                    <div class="flex items-center space-x-3">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face" 
                             class="w-10 h-10 rounded-full object-cover">
                        <div>
                            <h3 class="font-semibold text-gray-900">产品文案撰写</h3>
                            <p class="text-sm text-gray-500">电商平台</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-lg font-bold text-green-600">¥80</div>
                        <div class="text-xs text-gray-500">单价</div>
                    </div>
                </div>
                <p class="text-gray-600 text-sm mb-3">为新上线的智能手表撰写产品介绍文案，要求突出产品特色，字数500-800字。</p>
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                        <span><i class="fas fa-clock mr-1"></i>2小时</span>
                        <span><i class="fas fa-users mr-1"></i>还需8人</span>
                        <span><i class="fas fa-star mr-1 text-yellow-400"></i>4.9</span>
                    </div>
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-medium hover:bg-blue-700 transition-colors">
                        立即接取
                    </button>
                </div>
            </div>

            <!-- 任务卡片3 -->
            <div class="task-card bg-white rounded-2xl p-5 shadow-sm border border-gray-100">
                <div class="flex items-start justify-between mb-3">
                    <div class="flex items-center space-x-3">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face" 
                             class="w-10 h-10 rounded-full object-cover">
                        <div>
                            <h3 class="font-semibold text-gray-900">店铺拍照任务</h3>
                            <p class="text-sm text-gray-500">餐饮连锁</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-lg font-bold text-green-600">¥25</div>
                        <div class="text-xs text-gray-500">单价</div>
                    </div>
                </div>
                <p class="text-gray-600 text-sm mb-3">需要到指定餐厅拍摄门店外观和内部环境照片，要求清晰度高，角度多样。</p>
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                        <span><i class="fas fa-clock mr-1"></i>45分钟</span>
                        <span><i class="fas fa-users mr-1"></i>还需20人</span>
                        <span><i class="fas fa-star mr-1 text-yellow-400"></i>4.7</span>
                    </div>
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-medium hover:bg-blue-700 transition-colors">
                        立即接取
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="tab-bar">
        <div class="tab-item active">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-tasks"></i>
            <span>我的任务</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-plus-circle"></i>
            <span>发布</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-comments"></i>
            <span>消息</span>
        </div>
        <div class="tab-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html>
